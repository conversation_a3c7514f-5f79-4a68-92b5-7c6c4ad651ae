import { useEffect, useRef, useCallback, useMemo } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';

export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { isNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessor = useRef(null);
  const originalTrack = useRef(null);
  const needsDelayRef = useRef(true);

  // Memoized mic publication getter
  const getMicPublication = useCallback(() =>
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone), [room]);

  // Cleanup function to stop noise suppression
  const stopNoiseSuppressionAsync = useCallback(async () => {
    if (!noiseProcessor.current) return;

    try {
      console.log('🔇 Stopping noise suppression...');
      const micPublication = getMicPublication();

      if (micPublication?.track && originalTrack.current) {
        await micPublication.track.replaceTrack(originalTrack.current, true);
        console.log('✅ Original track restored');
      }

      noiseProcessor.current.stopProcessing();
      noiseProcessor.current = null;
      originalTrack.current = null;
      console.log('🧹 Noise processor cleaned up');
    } catch (error) {
      console.error('❌ Error stopping noise suppression:', error);
    }
  }, [getMicPublication]);

  // Apply noise suppression with smart delay logic
  const applyNoiseSuppression = useCallback(async (micPublication, shouldDelay = false) => {
    const localAudioTrack = micPublication.track;
    const applyLogic = async () => {
      try {
        const currentMicPublication = getMicPublication();
        const isStillValid = currentMicPublication?.isEnabled &&
                           currentMicPublication?.track &&
                           !currentMicPublication?.isMuted &&
                           isNoiseSuppressionEnabled &&
                           !noiseProcessor.current;

        if (!isStillValid) {
          console.log('⏹️ Conditions changed, skipping noise suppression');
          return;
        }

        console.log('🔊 Applying noise suppression...');
        originalTrack.current = localAudioTrack.mediaStreamTrack;
        noiseProcessor.current = new NoiseSuppressionProcessor();

        const processedTrack = await noiseProcessor.current.startProcessing(localAudioTrack.mediaStreamTrack);
        if (processedTrack) {
          await localAudioTrack.replaceTrack(processedTrack, true);
          console.log('✅ Noise suppression applied successfully');
          needsDelayRef.current = false; 
        }
      } catch (error) {
        console.error('❌ Noise suppression error:', error);
      }
    };

    if (shouldDelay && needsDelayRef.current) {
      console.log('🔊 Letting mic work normally first...');
      setTimeout(applyLogic, 1000);
    } else {
      await applyLogic();
    }
  }, [getMicPublication, isNoiseSuppressionEnabled]);

  // Main noise suppression effect
  useEffect(() => {
    if (!room?.localParticipant) return;

    const micPublication = getMicPublication();
    const micState = useMemo(() => ({
      isEnabled: micPublication?.isEnabled,
      hasTrack: !!micPublication?.track,
      isMuted: micPublication?.isMuted,
      isPublished: micPublication?.track && !micPublication?.isMuted
    }), [micPublication]);

    // Debug logging
    console.log('🎤 Microphone State:', {
      ...micState,
      isNoiseSuppressionEnabled,
      hasNoiseProcessor: !!noiseProcessor.current,
      needsDelay: needsDelayRef.current,
      deviceIdAudio
    });

    const handleNoiseSuppressionAsync = async () => {
      const { isEnabled, hasTrack, isMuted, isPublished } = micState;

      // Should stop NS?
      if ((!isEnabled || !hasTrack || !isPublished || !isNoiseSuppressionEnabled) && noiseProcessor.current) {
        console.log('🛑 Stopping noise suppression...');
        await stopNoiseSuppressionAsync();
        return;
      }

      // Should start NS?
      if (isEnabled && hasTrack && isPublished && isNoiseSuppressionEnabled && !noiseProcessor.current) {
        // Determine if delay is needed (first time or device change)
        const shouldDelay = needsDelayRef.current;
        await applyNoiseSuppression(micPublication, shouldDelay);
        return;
      }

      // Waiting states
      if (isNoiseSuppressionEnabled && !hasTrack) {
        console.log('🎛️ Waiting for microphone...');
      } else if (isNoiseSuppressionEnabled && hasTrack && isMuted) {
        console.log('🔇 Waiting for microphone to be unmuted...');
      }
    };

    handleNoiseSuppressionAsync();
  }, [
    getMicPublication()?.isEnabled,
    getMicPublication()?.track,
    getMicPublication()?.isMuted,
    deviceIdAudio, // Reset delay flag on device change
    isNoiseSuppressionEnabled,
    applyNoiseSuppression,
    stopNoiseSuppressionAsync
  ]);

  // Reset delay flag when device changes
  useEffect(() => {
    needsDelayRef.current = true;
  }, [deviceIdAudio]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component cleanup - stopping noise suppression');
      stopNoiseSuppressionAsync();
    };
  }, [stopNoiseSuppressionAsync]);

  return useMemo(() => ({
    isNoiseSuppressionActive: !!noiseProcessor.current,
    hasOriginalTrack: !!originalTrack.current,
    stopNoiseSuppression: stopNoiseSuppressionAsync
  }), [stopNoiseSuppressionAsync]);
};
